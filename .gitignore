# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
/dist
dist/
/dist/
lib/
build/
.next/
out/
.git/

dist-ssr
coverage
*.local
.env
.env.local
.env.*.local

# 其他配置文件（按需放开）
# .env.development
# .env.production
# .env.test
# .env.example

# Windows
[Dd]esktop.ini
Thumbs.db
$RECYCLE.BIN/

# macOS
.DS_Store
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes

# 编辑器/IDE
.vscode/*
!.vscode/extensions.json
.sublime-project
.sublime-workspace
.atom/
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?