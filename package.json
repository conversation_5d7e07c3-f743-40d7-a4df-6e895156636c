{"name": "components-monorepo", "version": "1.0.0", "description": "A monorepo containing table component library and storybook", "private": true, "type": "module", "scripts": {"dev": "pnpm --parallel dev", "build": "pnpm --recursive build", "build:table": "pnpm --filter table build", "build:storybook": "pnpm --filter storybook build", "storybook": "pnpm --filter storybook storybook", "storybook:build": "pnpm --filter storybook build-storybook", "table": "pnpm --filter table storybook", "lint": "pnpm --recursive lint", "type-check": "pnpm --recursive type-check", "clean": "pnpm --recursive clean", "install:all": "pnpm install"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "workspaces": ["packages/*"], "pnpm-workspaces": ["packages/*"], "packageManager": "pnpm@8.15.0", "volta": {"node": "22.17.0"}}