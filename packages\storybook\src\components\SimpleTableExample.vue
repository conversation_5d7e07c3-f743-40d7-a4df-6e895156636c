<template>
  <div class="simple-table-example">
    <h2 class="title">简单表格示例</h2>
    
    <div class="controls">
      <button @click="toggleLoading" class="btn">
        {{ loading ? '停止加载' : '开始加载' }}
      </button>
      <button @click="addUser" class="btn btn-primary">
        添加用户
      </button>
      <button @click="removeLastUser" class="btn btn-danger" :disabled="users.length === 0">
        删除最后一个
      </button>
    </div>

    <div class="info">
      <p>当前用户数量: <strong>{{ users.length }}</strong></p>
      <p>表格状态: <strong>{{ loading ? '加载中' : '正常' }}</strong></p>
    </div>

    <DataTable
      :columns="columns"
      :data="users"
      :loading="loading"
      :bordered="true"
      :hoverable="true"
      :striped="false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { DataTable } from '@components/table'
import type { TableColumn, TableData } from '@components/table'

// 响应式数据
const loading = ref(false)
const users = ref<TableData[]>([
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    department: '技术部',
    status: 'active',
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    department: '产品部',
    status: 'pending',
  }
])

// 表格列配置
const columns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: 60,
    sortable: true,
  },
  {
    key: 'name',
    title: '姓名',
    width: 100,
    sortable: true,
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    align: 'center',
    sortable: true,
  },
  {
    key: 'email',
    title: '邮箱',
    width: 180,
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    align: 'center',
    render: (value: string) => {
      const statusConfig = {
        active: { text: '活跃', color: 'green' },
        pending: { text: '待审核', color: 'yellow' },
        inactive: { text: '非活跃', color: 'red' }
      }
      const config = statusConfig[value as keyof typeof statusConfig] || { text: value, color: 'gray' }
      return `<span class="status-badge status-${config.color}">${config.text}</span>`
    },
  },
]

// 方法
const toggleLoading = () => {
  loading.value = !loading.value
}

const addUser = () => {
  const newId = Math.max(...users.value.map(u => u.id)) + 1
  const names = ['王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const departments = ['技术部', '产品部', '设计部', '运营部']
  const statuses = ['active', 'pending', 'inactive']
  
  const newUser = {
    id: newId,
    name: names[Math.floor(Math.random() * names.length)],
    age: Math.floor(Math.random() * 20) + 25,
    email: `user${newId}@example.com`,
    department: departments[Math.floor(Math.random() * departments.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
  }
  
  users.value.push(newUser)
}

const removeLastUser = () => {
  if (users.value.length > 0) {
    users.value.pop()
  }
}
</script>

<style scoped>
.simple-table-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #333;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn:hover {
  background: #f5f5f5;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.btn-danger:hover {
  background: #c82333;
}

.info {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 14px;
}

.info p {
  margin: 5px 0;
}

/* 状态徽章样式 */
:deep(.status-badge) {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

:deep(.status-green) {
  background: #d4edda;
  color: #155724;
}

:deep(.status-yellow) {
  background: #fff3cd;
  color: #856404;
}

:deep(.status-red) {
  background: #f8d7da;
  color: #721c24;
}

:deep(.status-gray) {
  background: #e2e3e5;
  color: #383d41;
}
</style>
