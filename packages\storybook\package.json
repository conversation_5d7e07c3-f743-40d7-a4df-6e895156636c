{"name": "@components/storybook", "version": "1.0.0", "private": true, "type": "module", "scripts": {"storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "serve-storybook": "npx http-server storybook-static", "clean": "<PERSON><PERSON><PERSON> storybook-static"}, "dependencies": {"@components/table": "workspace:*", "vue": "^3.5.17"}, "devDependencies": {"@storybook/addon-docs": "^9.0.15", "@storybook/addon-links": "^9.0.15", "@storybook/vue3": "^9.0.15", "@storybook/vue3-vite": "^9.0.15", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "rimraf": "^5.0.5", "storybook": "^9.0.15", "typescript": "~5.8.0", "vite": "^7.0.0"}, "volta": {"node": "22.17.0"}}