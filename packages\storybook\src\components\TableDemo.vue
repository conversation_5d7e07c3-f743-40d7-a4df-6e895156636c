<template>
  <div class="table-demo">
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3 class="text-lg font-semibold mb-4">表格控制面板</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <!-- 数据控制 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">数据操作</label>
          <div class="flex flex-col gap-2">
            <button 
              @click="addRandomUser" 
              class="btn btn-primary"
              :disabled="loading"
            >
              添加随机用户
            </button>
            <button 
              @click="refreshData" 
              class="btn btn-secondary"
              :disabled="loading"
            >
              {{ loading ? '刷新中...' : '刷新数据' }}
            </button>
            <button 
              @click="clearData" 
              class="btn btn-danger"
              :disabled="loading"
            >
              清空数据
            </button>
          </div>
        </div>

        <!-- 表格样式控制 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">表格样式</label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input 
                v-model="tableConfig.bordered" 
                type="checkbox" 
                class="mr-2"
              >
              显示边框
            </label>
            <label class="flex items-center">
              <input 
                v-model="tableConfig.striped" 
                type="checkbox" 
                class="mr-2"
              >
              斑马纹
            </label>
            <label class="flex items-center">
              <input 
                v-model="tableConfig.hoverable" 
                type="checkbox" 
                class="mr-2"
              >
              悬停效果
            </label>
            <label class="flex items-center">
              <input 
                v-model="tableConfig.showHeader" 
                type="checkbox" 
                class="mr-2"
              >
              显示表头
            </label>
          </div>
        </div>

        <!-- 分页控制 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">分页设置</label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input 
                v-model="tableConfig.pagination" 
                type="checkbox" 
                class="mr-2"
              >
              启用分页
            </label>
            <div v-if="tableConfig.pagination">
              <label class="block text-xs text-gray-600">每页条数</label>
              <select v-model="tableConfig.pageSize" class="w-full border rounded px-2 py-1">
                <option :value="5">5 条/页</option>
                <option :value="10">10 条/页</option>
                <option :value="20">20 条/页</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="stats-panel mb-4">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="stat-card">
          <div class="stat-number">{{ tableData.length }}</div>
          <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ activeUsers }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ pendingUsers }}</div>
          <div class="stat-label">待审核</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ inactiveUsers }}</div>
          <div class="stat-label">非活跃</div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :bordered="tableConfig.bordered"
      :striped="tableConfig.striped"
      :hoverable="tableConfig.hoverable"
      :show-header="tableConfig.showHeader"
      :pagination="tableConfig.pagination"
      :page-size="tableConfig.pageSize"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { DataTable } from '@components/table'
import type { TableColumn, TableData } from '@components/table'

// 响应式数据
const loading = ref(false)
const tableData = ref<TableData[]>([])

// 表格配置
const tableConfig = ref({
  bordered: true,
  striped: false,
  hoverable: true,
  showHeader: true,
  pagination: false,
  pageSize: 10
})

// 列配置
const columns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: 80,
    sortable: true,
  },
  {
    key: 'name',
    title: '姓名',
    width: 120,
    sortable: true,
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    align: 'center',
    sortable: true,
  },
  {
    key: 'email',
    title: '邮箱',
    width: 200,
  },
  {
    key: 'department',
    title: '部门',
    width: 120,
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    align: 'center',
    render: (value: string) => {
      const statusMap: Record<string, { text: string; class: string }> = {
        active: {
          text: '活跃',
          class: 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs',
        },
        inactive: {
          text: '非活跃',
          class: 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs',
        },
        pending: {
          text: '待审核',
          class: 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs',
        },
      }
      const status = statusMap[value] || { text: value, class: '' }
      return `<span class="${status.class}">${status.text}</span>`
    },
  },
  {
    key: 'actions',
    title: '操作',
    width: 150,
    align: 'center',
    render: (_, record: TableData) => {
      return `
        <div class="flex gap-2 justify-center">
          <button 
            class="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            onclick="alert('编辑用户: ${record.name}')"
          >
            编辑
          </button>
          <button 
            class="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            onclick="if(confirm('确定要删除用户 ${record.name} 吗？')) { window.deleteUser(${record.id}) }"
          >
            删除
          </button>
        </div>
      `
    },
  },
]

// 计算属性
const activeUsers = computed(() => 
  tableData.value.filter(user => user.status === 'active').length
)

const pendingUsers = computed(() => 
  tableData.value.filter(user => user.status === 'pending').length
)

const inactiveUsers = computed(() => 
  tableData.value.filter(user => user.status === 'inactive').length
)

// 方法
const addRandomUser = () => {
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const departments = ['技术部', '产品部', '设计部', '运营部', '市场部']
  const statuses = ['active', 'inactive', 'pending']
  
  const newId = Math.max(...tableData.value.map(item => item.id), 0) + 1
  const randomName = names[Math.floor(Math.random() * names.length)]
  
  const newUser = {
    id: newId,
    name: `${randomName}${newId}`,
    age: Math.floor(Math.random() * 30) + 20,
    email: `user${newId}@example.com`,
    department: departments[Math.floor(Math.random() * departments.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
  }
  
  tableData.value.push(newUser)
}

const refreshData = async () => {
  loading.value = true
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 重新生成数据
  tableData.value = Array.from({ length: 8 }, (_, i) => ({
    id: i + 1,
    name: `用户${i + 1}`,
    age: 20 + (i % 30),
    email: `user${i + 1}@example.com`,
    department: ['技术部', '产品部', '设计部', '运营部'][i % 4],
    status: ['active', 'inactive', 'pending'][i % 3],
  }))
  
  loading.value = false
}

const clearData = () => {
  if (confirm('确定要清空所有数据吗？')) {
    tableData.value = []
  }
}

// 全局删除函数（用于 HTML 字符串中的 onclick）
;(window as any).deleteUser = (id: number) => {
  const index = tableData.value.findIndex(item => item.id === id)
  if (index > -1) {
    tableData.value.splice(index, 1)
  }
}

// 初始化数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.table-demo {
  @apply p-6 max-w-7xl mx-auto;
}

.control-panel {
  @apply bg-gray-50 rounded-lg p-4 mb-6;
}

.btn {
  @apply px-3 py-2 rounded text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.btn-secondary {
  @apply bg-gray-500 text-white hover:bg-gray-600;
}

.btn-danger {
  @apply bg-red-500 text-white hover:bg-red-600;
}

.stats-panel {
  @apply bg-white rounded-lg border p-4;
}

.stat-card {
  @apply text-center p-3 bg-gray-50 rounded;
}

.stat-number {
  @apply text-2xl font-bold text-blue-600;
}

.stat-label {
  @apply text-sm text-gray-600;
}
</style>
