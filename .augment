# Augment 规则文件 - Components Monorepo

## 项目结构
这是一个基于 pnpm workspace 的 monorepo 项目，包含以下子项目：

### packages/table
- **类型**: Vue3 组件库
- **技术栈**: Vite + Vue3 + TypeScript + TailwindCSS
- **用途**: 提供可复用的表格组件
- **构建**: 库模式构建，输出 ES 模块
- **主要文件**:
  - `src/index.ts`: 库入口文件
  - `src/components/DataTable.vue`: 主要的表格组件
  - `src/types/index.ts`: TypeScript 类型定义
  - `src/style.css`: 样式文件（包含 TailwindCSS）

### packages/storybook
- **类型**: Storybook 文档项目
- **用途**: 展示和测试 table 库中的组件
- **依赖**: 引用 @components/table 库
- **主要文件**:
  - `.storybook/main.ts`: Storybook 主配置
  - `.storybook/preview.ts`: 预览配置
  - `src/DataTable.stories.ts`: DataTable 组件的 Stories

## 开发工作流

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
# 启动所有项目的开发模式
pnpm dev

# 只启动 table 库开发
pnpm --filter table dev

# 启动 Storybook
pnpm storybook
```

### 构建
```bash
# 构建所有项目
pnpm build

# 只构建 table 库
pnpm build:table

# 构建 Storybook
pnpm build:storybook
```

### 类型检查
```bash
pnpm type-check
```

## 项目约定

### 命名规范
- 包名使用 `@components/` 前缀
- 组件名使用 PascalCase
- 文件名使用 kebab-case 或 PascalCase

### 技术栈选择
- **包管理器**: pnpm（支持 workspace）
- **构建工具**: Vite
- **前端框架**: Vue3 + TypeScript
- **样式**: TailwindCSS
- **文档**: Storybook

### 依赖管理
- 使用 `workspace:*` 引用内部包
- 共享依赖放在根目录
- 特定依赖放在各自的 package.json

### 代码组织
- 组件库采用库模式构建
- 导出清晰的 API 接口
- 提供完整的 TypeScript 类型定义
- 样式与组件分离，支持按需引入

## 常见任务

### 添加新组件到 table 库
1. 在 `packages/table/src/components/` 创建组件
2. 在 `packages/table/src/types/` 添加类型定义
3. 在 `packages/table/src/index.ts` 导出组件
4. 在 `packages/storybook/src/` 创建对应的 Stories

### 更新依赖
```bash
# 更新所有依赖
pnpm update

# 更新特定包的依赖
pnpm --filter table update
pnpm --filter storybook update
```

### 清理构建产物
```bash
pnpm clean
```

## 注意事项
- 修改 table 库后需要重新构建才能在 Storybook 中看到变化
- 使用 TypeScript 严格模式，确保类型安全
- TailwindCSS 配置在各个项目中独立管理
- Storybook 通过 workspace 依赖引用 table 库

## 参考资料
vtable https://visactor.io/vtable