export interface TableColumn {
  key: string
  title: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  render?: (value: any, record: any, index: number) => any
}

export interface TableData {
  [key: string]: any
}

export interface TableProps {
  columns: TableColumn[]
  data: TableData[]
  loading?: boolean
  pagination?: boolean
  pageSize?: number
  showHeader?: boolean
  bordered?: boolean
  striped?: boolean
  hoverable?: boolean
}
